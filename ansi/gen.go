//go:build ignore
// +build ignore

package main

import (
	"bytes"
	"fmt"
	"go/format"
	"log"
	"os"

	. "github.com/charmbracelet/x/ansi/parser"
)

func main() {
	var f bytes.Buffer
	table := GenerateTransitionTable()
	_, _ = f.WriteString(`package parser

// Code generated by gen.go. DO NOT EDIT.

// Table is a DEC ANSI transition table.
var Table = TransitionTable{
`)
	for i, v := range table {
		code := i & 0xFF
		state := v & TransitionStateMask
		action := v >> TransitionActionShift
		next := v & TransitionStateMask

		format := "\t%s<<TransitionActionShift"
		args := []interface{}{ActionNames[action]}
		if next > GroundState {
			format += " | %s"
			args = append(args, StateNames[next])
		}
		format += ","
		fmt.Fprintf(&f, format, args...)
		fmt.Fprintf(&f, " // %d: %s << IndexStateShift | 0x%02x", i, StateNames[state], code)
		fmt.Fprintln(&f)
	}

	fmt.Fprintln(&f, "}")
	content, err := format.Source(f.Bytes())
	if err != nil {
		log.Fatalf("formatting source: %v", err)
	}

	if err := os.WriteFile("parser/table.go", content, os.ModePerm); err != nil {
		log.Fatalf("writing file: %v", err)
	}
}
