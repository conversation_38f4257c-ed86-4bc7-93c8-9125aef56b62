package higherorder

// <PERSON><PERSON><PERSON> applies a function to each element of a list, starting from the left.
// A single value is returned.
func Foldl[A any](f func(x, y A) A, start A, list []A) A {
	for _, v := range list {
		start = f(start, v)
	}
	return start
}

// <PERSON><PERSON><PERSON> applies a function to each element of a list, starting from the right.
// A single value is returned.
func Foldr[A any](f func(x, y A) A, start A, list []A) A {
	for i := len(list) - 1; i >= 0; i-- {
		start = f(start, list[i])
	}
	return start
}

// Map applies a given function to each element of a list, returning a new list.
func Map[A, B any](f func(A) B, list []A) []B {
	res := make([]B, len(list))
	for i, v := range list {
		res[i] = f(v)
	}
	return res
}

// Filter applies a function to each element of a list, if the function returns false those elements are removed, returning a new list
func Filter[A any](f func(A) bool, list []A) []A {
	res := make([]A, 0)
	for _, v := range list {
		if f(v) {
			res = append(res, v)
		}
	}
	return res
}
