# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

!go.work

*.png

# MacOS invisible file
.DS_Store

# Allow graphics used for bench test
!ansi/fixtures/graphics/*.png
!mosaic/fixtures/*.png
