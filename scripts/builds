#!/usr/bin/env bash

# shellcheck disable=SC2016
find . -type f -name go.mod | sort | while read -r mod; do
  dir="$(dirname "$mod")"
  name="$(basename "$dir")"
  # get the parent directory when the module is nested semver-style
  if [[ "$name" =~ ^v[0-9]+.*$ ]]; then
    ver="$(basename "$dir")"
    dir="$(dirname "$dir")"
    name="$(basename "$dir")-$ver"
  fi

  sum="$dir/go.sum"
  echo "# auto-generated by scripts/builds. DO NOT EDIT.
name: $name

on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - $(dirname "$mod" | cut -f2- -d/)/**
      - .github/workflows/${name}.yml

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: \${{ matrix.os }}
    defaults:
      run:
        working-directory: $(dirname "$mod")
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: $mod
          cache: true
          cache-dependency-path: $sum
      - run: go build -v ./...
      - run: go test -race -v ./...
  dependabot:
    needs: [build]
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    if: \${{ github.actor == 'dependabot[bot]' && github.event_name == 'pull_request'}}
    steps:
      - id: metadata
        uses: dependabot/fetch-metadata@v2
        with:
          github-token: \"\${{ secrets.GITHUB_TOKEN }}\"
      - run: |
          gh pr review --approve \"\$PR_URL\"
          gh pr merge --squash --auto \"\$PR_URL\"
        env:
          PR_URL: \${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: \${{secrets.GITHUB_TOKEN}}
  lint:
    uses: charmbracelet/meta/.github/workflows/lint.yml@main
    with:
      directory: $(dirname "$mod")/...
" >"./.github/workflows/${name}.yml"
done
