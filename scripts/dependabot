#!/usr/bin/env bash

echo '# auto-generated by scripts/dependabot. DO NOT EDIT.

version: 2

updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "05:00"
      timezone: "America/New_York"
    labels:
      - "dependencies"
    commit-message:
      prefix: "chore"
      include: "scope"' >./.github/dependabot.yml

find . -type f -name go.mod | cut -f2- -d'/' | sort | while read -r mod; do
  echo '
  - package-ecosystem: "gomod"
    directory: "/'"$(dirname "$mod")"'"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "05:00"
      timezone: "America/New_York"
    labels:
      - "dependencies"
    commit-message:
      prefix: "chore"
      include: "scope"' >>./.github/dependabot.yml
done
