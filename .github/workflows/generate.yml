name: generate

on:
  push:
    branches:
      - "main"
  workflow_dispatch: {}

permissions:
  contents: write
  actions: write

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: stable
          cache: true
      - run: ./scripts/dependabot
      - run: ./scripts/builds
      - uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "ci: auto-update configuration"
          branch: main
          commit_user_name: actions-user
          commit_user_email: <EMAIL>
