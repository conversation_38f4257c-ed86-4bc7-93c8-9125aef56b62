# auto-generated by scripts/builds. DO NOT EDIT.
name: termios

on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - termios/**
      - .github/workflows/termios.yml

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    defaults:
      run:
        working-directory: ./termios
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: ./termios/go.mod
          cache: true
          cache-dependency-path: ./termios/go.sum
      - run: go build -v ./...
      - run: go test -race -v ./...
  dependabot:
    needs: [build]
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    if: ${{ github.actor == 'dependabot[bot]' && github.event_name == 'pull_request'}}
    steps:
      - id: metadata
        uses: dependabot/fetch-metadata@v2
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
      - run: |
          gh pr review --approve "$PR_URL"
          gh pr merge --squash --auto "$PR_URL"
        env:
          PR_URL: ${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
  lint:
    uses: charmbracelet/meta/.github/workflows/lint.yml@main
    with:
      directory: ./termios/...

