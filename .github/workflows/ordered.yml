# auto-generated by scripts/builds. DO NOT EDIT.
name: ordered

on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - exp/ordered/**
      - .github/workflows/ordered.yml

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    defaults:
      run:
        working-directory: ./exp/ordered
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: ./exp/ordered/go.mod
          cache: true
          cache-dependency-path: ./exp/ordered/go.sum
      - run: go build -v ./...
      - run: go test -race -v ./...
  dependabot:
    needs: [build]
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    if: ${{ github.actor == 'dependabot[bot]' && github.event_name == 'pull_request'}}
    steps:
      - id: metadata
        uses: dependabot/fetch-metadata@v2
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
      - run: |
          gh pr review --approve "$PR_URL"
          gh pr merge --squash --auto "$PR_URL"
        env:
          PR_URL: ${{github.event.pull_request.html_url}}
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
  lint:
    uses: charmbracelet/meta/.github/workflows/lint.yml@main
    with:
      directory: ./exp/ordered/...

