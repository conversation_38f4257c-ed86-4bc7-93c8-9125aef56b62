name: termios-goos
on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - "termios/**"

jobs:
  test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./termios
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: ./termios/go.mod
          cache: true
          cache-dependency-path: ./termios/go.sum
      # https://go.dev/doc/install/source#environment
      - run: GOOS=darwin GOARCH=amd64 go test -c -v ./...
      - run: GOOS=darwin GOARCH=arm64 go test -c -v ./...
      - run: GOOS=dragonfly GOARCH=amd64 go test -c -v ./...
      - run: GOOS=freebsd GOARCH=386 go test -c -v ./...
      - run: GOOS=freebsd GOARCH=amd64 go test -c -v ./...
      - run: GOOS=freebsd GOARCH=arm go test -c -v ./...
      - run: GOOS=linux GOARCH=386 go test -c -v ./...
      - run: GOOS=linux GOARCH=amd64 go test -c -v ./...
      - run: GOOS=linux GOARCH=arm go test -c -v ./...
      - run: GOOS=linux GOARCH=arm64 go test -c -v ./...
      - run: GOOS=linux GOARCH=mips go test -c -v ./...
      - run: GOOS=linux GOARCH=mips64 go test -c -v ./...
      - run: GOOS=linux GOARCH=mips64le go test -c -v ./...
      - run: GOOS=linux GOARCH=mipsle go test -c -v ./...
      - run: GOOS=linux GOARCH=ppc64 go test -c -v ./...
      - run: GOOS=linux GOARCH=ppc64le go test -c -v ./...
      - run: GOOS=linux GOARCH=riscv64 go test -c -v ./...
      - run: GOOS=linux GOARCH=s390x go test -c -v ./...
      - run: GOOS=netbsd GOARCH=386 go test -c -v ./...
      - run: GOOS=netbsd GOARCH=amd64 go test -c -v ./...
      - run: GOOS=netbsd GOARCH=arm go test -c -v ./...
      - run: GOOS=openbsd GOARCH=386 go test -c -v ./...
      - run: GOOS=openbsd GOARCH=amd64 go test -c -v ./...
      - run: GOOS=openbsd GOARCH=arm go test -c -v ./...
      - run: GOOS=openbsd GOARCH=arm64 go test -c -v ./...
      - run: GOOS=solaris GOARCH=amd64 go test -c -v ./...

      # unsupported
      # - run: GOOS=aix	GOARCH=ppc64 go test -c -v ./...
      # - run: GOOS=android GOARCH=386 go test -c -v ./...
      # - run: GOOS=android GOARCH=amd64 go test -c -v ./...
      # - run: GOOS=android GOARCH=arm go test -c -v ./...
      # - run: GOOS=android GOARCH=arm64 go test -c -v ./...
      # - run: GOOS=illumos GOARCH=amd64 go test -c -v ./...
      # - run: GOOS=ios GOARCH=arm64 go test -c -v ./...
      # - run: GOOS=js GOARCH=wasm go test -c -v ./...
      # - run: GOOS=linux GOARCH=loong64 go test -c -v ./...
      # - run: GOOS=plan9 GOARCH=386 go test -c -v ./...
      # - run: GOOS=plan9 GOARCH=amd64 go test -c -v ./...
      # - run: GOOS=plan9 GOARCH=arm go test -c -v ./...
      # - run: GOOS=wasip1 GOARCH=wasm go test -c -v ./...
      # - run: GOOS=windows GOARCH=386 go test -c -v ./...
      # - run: GOOS=windows GOARCH=amd64 go test -c -v ./...
      # - run: GOOS=windows GOARCH=arm go test -c -v ./...
      # - run: GOOS=windows GOARCH=arm64 go test -c -v ./...
