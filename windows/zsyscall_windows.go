// Code generated by 'go generate'; DO NOT EDIT.

package windows

import (
	"syscall"
	"unsafe"
)

var _ unsafe.Pointer

// Do the interface allocations only once for common
// Errno values.
const (
	errnoERROR_IO_PENDING = 997
)

var (
	errERROR_IO_PENDING error = syscall.Errno(errnoERROR_IO_PENDING)
	errERROR_EINVAL     error = syscall.EINVAL
)

// errnoErr returns common boxed Errno values, to prevent
// allocations at runtime.
func errnoErr(e syscall.Errno) error {
	switch e {
	case 0:
		return errERROR_EINVAL
	case errnoERROR_IO_PENDING:
		return errERROR_IO_PENDING
	}
	// TODO: add more here, after collecting data on the common
	// error values see on Windows. (perhaps when running
	// all.bat?)
	return e
}

var (
	modkernel32 = NewLazySystemDLL("kernel32.dll")

	procFlushConsoleInputBuffer       = modkernel32.NewProc("FlushConsoleInputBuffer")
	procGetNumberOfConsoleInputEvents = modkernel32.NewProc("GetNumberOfConsoleInputEvents")
	procPeekConsoleInputW             = modkernel32.NewProc("PeekConsoleInputW")
	procReadConsoleInputW             = modkernel32.NewProc("ReadConsoleInputW")
)

func FlushConsoleInputBuffer(console Handle) (err error) {
	r1, _, e1 := syscall.Syscall(procFlushConsoleInputBuffer.Addr(), 1, uintptr(console), 0, 0)
	if r1 == 0 {
		err = errnoErr(e1)
	}
	return
}

func GetNumberOfConsoleInputEvents(console Handle, numevents *uint32) (err error) {
	r1, _, e1 := syscall.Syscall(procGetNumberOfConsoleInputEvents.Addr(), 2, uintptr(console), uintptr(unsafe.Pointer(numevents)), 0)
	if r1 == 0 {
		err = errnoErr(e1)
	}
	return
}

func PeekConsoleInput(console Handle, buf *InputRecord, toread uint32, read *uint32) (err error) {
	r1, _, e1 := syscall.Syscall6(procPeekConsoleInputW.Addr(), 4, uintptr(console), uintptr(unsafe.Pointer(buf)), uintptr(toread), uintptr(unsafe.Pointer(read)), 0, 0)
	if r1 == 0 {
		err = errnoErr(e1)
	}
	return
}

func ReadConsoleInput(console Handle, buf *InputRecord, toread uint32, read *uint32) (err error) {
	r1, _, e1 := syscall.Syscall6(procReadConsoleInputW.Addr(), 4, uintptr(console), uintptr(unsafe.Pointer(buf)), uintptr(toread), uintptr(unsafe.Pointer(read)), 0, 0)
	if r1 == 0 {
		err = errnoErr(e1)
	}
	return
}
