module github.com/charmbracelet/x/examples

go 1.23.0

require (
	github.com/charmbracelet/colorprofile v0.3.1
	github.com/charmbracelet/lipgloss v1.1.0
	github.com/charmbracelet/lipgloss/v2 v2.0.0-alpha.2.0.20250125233033-58a153eb00e6
	github.com/charmbracelet/x/ansi v0.9.2
	github.com/charmbracelet/x/cellbuf v0.0.13
	github.com/charmbracelet/x/exp/charmtone v0.0.0-20250602192518-9e722df69bbb
	github.com/charmbracelet/x/exp/toner v0.0.0-20250602202920-5fecc56e9a94
	github.com/charmbracelet/x/input v0.3.4
	github.com/charmbracelet/x/mosaic v0.0.0-20250313150240-c09addb0e197
	github.com/creack/pty v1.1.24
	github.com/lucasb-eyer/go-colorful v1.2.0
)

require (
	github.com/aymanbagabas/go-osc52/v2 v2.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.22.0 // indirect
	github.com/charmbracelet/x/windows v0.2.0 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/muesli/termenv v0.16.0 // indirect
)

require (
	github.com/charmbracelet/x/term v0.2.1
	github.com/muesli/cancelreader v0.2.2 // indirect
	github.com/rivo/uniseg v0.4.7
	github.com/xo/terminfo v0.0.0-20220910002029-abceb7e1c41e // indirect
	golang.org/x/image v0.25.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
)
