package main

import (
	"fmt"
	"os"
	"strconv"
	"unicode"

	"github.com/charmbracelet/x/termspec"
)

func main() {
	// Parse command line arguments
	var startCode, endCode int = 0x0080, 0xFFFF // Default 2-byte Unicode range
	var showOnlyPrintable bool = true

	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "-h", "--help":
			showHelp()
			return
		case "--all":
			showOnlyPrintable = false
		case "--ascii":
			startCode, endCode = 0x0000, 0x007F
		case "--extended":
			startCode, endCode = 0x0080, 0x07FF
		case "--cjk":
			startCode, endCode = 0x4E00, 0x9FFF
		case "--symbols":
			startCode, endCode = 0x2000, 0x2BFF
		default:
			// Try to parse as hex range
			if len(os.Args) >= 3 {
				if start, err := strconv.ParseInt(os.Args[1], 16, 32); err == nil {
					if end, err := strconv.ParseInt(os.Args[2], 16, 32); err == nil {
						startCode, endCode = int(start), int(end)
					}
				}
			}
		}
	}

	// Detect current terminal
	spec := termspec.DetectCurrent()
	
	fmt.Printf("Unicode Character Display Demo\n")
	fmt.Printf("==============================\n")
	fmt.Printf("Terminal: %s\n", spec.Name())
	fmt.Printf("Range: U+%04X to U+%04X\n", startCode, endCode)
	fmt.Printf("Show only printable: %t\n\n", showOnlyPrintable)
	fmt.Printf("Format: [Character] U+CODE (width) - Description\n")
	fmt.Printf("================================================\n\n")

	count := 0
	for codePoint := startCode; codePoint <= endCode; codePoint++ {
		r := rune(codePoint)
		
		// Skip if we only want printable characters and this isn't printable
		if showOnlyPrintable && (!unicode.IsPrint(r) || unicode.IsControl(r)) {
			continue
		}

		// Calculate column width using terminal specification
		width := spec.ColCount(r)
		
		// Get character category for description
		category := getUnicodeCategory(r)
		
		// Display the character information
		if unicode.IsPrint(r) && !unicode.IsControl(r) {
			fmt.Printf("[%c] U+%04X (%d) - %s\n", r, codePoint, width, category)
		} else {
			// For non-printable characters, show a placeholder
			fmt.Printf("[ ] U+%04X (%d) - %s (non-printable)\n", codePoint, width, category)
		}
		
		count++
		
		// Add a pause every 50 characters to make it readable
		if count%50 == 0 {
			fmt.Printf("\n--- Displayed %d characters so far ---\n", count)
			fmt.Printf("Press Enter to continue (or Ctrl+C to exit)...")
			fmt.Scanln()
			fmt.Println()
		}
	}
	
	fmt.Printf("\n=== Summary ===\n")
	fmt.Printf("Total characters displayed: %d\n", count)
	fmt.Printf("Range: U+%04X to U+%04X\n", startCode, endCode)
	fmt.Printf("Terminal specification: %s\n", spec.Name())
}

func showHelp() {
	fmt.Printf(`Unicode Character Display Demo

Usage: unicode-demo [OPTIONS] [START_HEX END_HEX]

OPTIONS:
  -h, --help     Show this help message
  --all          Show all characters including non-printable
  --ascii        Show ASCII range (U+0000 to U+007F)
  --extended     Show extended ASCII range (U+0080 to U+07FF)
  --cjk          Show CJK Unified Ideographs (U+4E00 to U+9FFF)
  --symbols      Show General Punctuation and Symbols (U+2000 to U+2BFF)

EXAMPLES:
  unicode-demo                    # Show 2-byte Unicode range (U+0080 to U+FFFF)
  unicode-demo --cjk              # Show CJK characters
  unicode-demo --all              # Show all characters including non-printable
  unicode-demo 2600 26FF          # Show Miscellaneous Symbols (U+2600 to U+26FF)
  unicode-demo 1F600 1F64F        # Show Emoticons (U+1F600 to U+1F64F)

The program displays each character with its Unicode code point and the number
of columns it takes in the current terminal.
`)
}

func getUnicodeCategory(r rune) string {
	switch {
	case unicode.IsLetter(r):
		if unicode.IsUpper(r) {
			return "Letter, Uppercase"
		} else if unicode.IsLower(r) {
			return "Letter, Lowercase"
		}
		return "Letter"
	case unicode.IsDigit(r):
		return "Number, Decimal Digit"
	case unicode.IsNumber(r):
		return "Number"
	case unicode.IsPunct(r):
		return "Punctuation"
	case unicode.IsSymbol(r):
		return "Symbol"
	case unicode.IsMark(r):
		return "Mark"
	case unicode.IsSpace(r):
		return "Separator, Space"
	case unicode.IsControl(r):
		return "Other, Control"
	default:
		return "Other"
	}
}
