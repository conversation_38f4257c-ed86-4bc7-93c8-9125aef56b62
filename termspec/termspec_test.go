package termspec

import (
	"os"
	"testing"
)

func TestDetectCurrent(t *testing.T) {
	// Test that DetectCurrent returns a valid specification
	spec := DetectCurrent()
	if spec == nil {
		t.<PERSON>al("DetectCurrent returned nil")
	}

	// Test that the specification has a name
	name := spec.Name()
	if name == "" {
		t.<PERSON>r("Specification name is empty")
	}

	t.Logf("Detected terminal: %s", name)
}

func TestDefaultSpec(t *testing.T) {
	spec := &DefaultSpec{}

	// Test name
	if spec.Name() != "default" {
		t.<PERSON>rf("Expected name 'default', got '%s'", spec.Name())
	}

	// Test detect (should always return false)
	if spec.Detect() {
		t.<PERSON>r("DefaultSpec.Detect() should return false")
	}

	// Test ColCount with various runes
	testCases := []struct {
		rune     rune
		expected int
	}{
		{'a', 1},  // ASCII letter
		{'1', 1},  // ASCII digit
		{' ', 1},  // Space
		{'中', 2},  // Wide character (Chinese)
		{'🚀', 2},  // Emoji
		{'\t', 0}, // Tab (control character)
	}

	for _, tc := range testCases {
		width := spec.ColCount(tc.rune)
		if width < 0 {
			t.Errorf("ColCount(%q) returned negative width: %d", tc.rune, width)
		}
		// Note: We don't test exact values as they may vary between Unicode versions
		t.Logf("ColCount(%q) = %d", tc.rune, width)
	}
}

func TestKittySpec(t *testing.T) {
	spec := &KittySpec{}

	// Test name
	if spec.Name() != "kitty" {
		t.Errorf("Expected name 'kitty', got '%s'", spec.Name())
	}

	// Test detection with environment variables
	originalEnv := map[string]string{
		"KITTY_WINDOW_ID": os.Getenv("KITTY_WINDOW_ID"),
		"KITTY_PID":       os.Getenv("KITTY_PID"),
		"TERM":            os.Getenv("TERM"),
		"TERM_PROGRAM":    os.Getenv("TERM_PROGRAM"),
	}

	// Clean environment
	for key := range originalEnv {
		os.Unsetenv(key)
	}

	// Should not detect without environment variables
	if spec.Detect() {
		t.Error("KittySpec should not detect without environment variables")
	}

	// Test with KITTY_WINDOW_ID
	os.Setenv("KITTY_WINDOW_ID", "1")
	if !spec.Detect() {
		t.Error("KittySpec should detect with KITTY_WINDOW_ID")
	}
	os.Unsetenv("KITTY_WINDOW_ID")

	// Test with TERM
	os.Setenv("TERM", "xterm-kitty")
	if !spec.Detect() {
		t.Error("KittySpec should detect with TERM=xterm-kitty")
	}
	os.Unsetenv("TERM")

	// Test with TERM_PROGRAM
	os.Setenv("TERM_PROGRAM", "kitty")
	if !spec.Detect() {
		t.Error("KittySpec should detect with TERM_PROGRAM=kitty")
	}

	// Restore original environment
	for key, value := range originalEnv {
		if value != "" {
			os.Setenv(key, value)
		} else {
			os.Unsetenv(key)
		}
	}
}

func TestITerm2Spec(t *testing.T) {
	spec := &ITerm2Spec{}

	// Test name
	if spec.Name() != "iterm2" {
		t.Errorf("Expected name 'iterm2', got '%s'", spec.Name())
	}

	// Test basic functionality
	width := spec.ColCount('a')
	if width < 0 {
		t.Errorf("ColCount('a') returned negative width: %d", width)
	}
}

func TestAlacrittySpec(t *testing.T) {
	spec := &AlacrittySpec{}

	// Test name
	if spec.Name() != "alacritty" {
		t.Errorf("Expected name 'alacritty', got '%s'", spec.Name())
	}

	// Test basic functionality
	width := spec.ColCount('a')
	if width < 0 {
		t.Errorf("ColCount('a') returned negative width: %d", width)
	}
}

func TestWezTermSpec(t *testing.T) {
	spec := &WezTermSpec{}

	// Test name
	if spec.Name() != "wezterm" {
		t.Errorf("Expected name 'wezterm', got '%s'", spec.Name())
	}

	// Test basic functionality
	width := spec.ColCount('a')
	if width < 0 {
		t.Errorf("ColCount('a') returned negative width: %d", width)
	}
}

func TestXTermSpec(t *testing.T) {
	spec := &XTermSpec{}

	// Test name
	if spec.Name() != "xterm" {
		t.Errorf("Expected name 'xterm', got '%s'", spec.Name())
	}

	// Test basic functionality
	width := spec.ColCount('a')
	if width < 0 {
		t.Errorf("ColCount('a') returned negative width: %d", width)
	}
}

func TestTmuxSpec(t *testing.T) {
	spec := &TmuxSpec{}

	// Test name
	if spec.Name() != "tmux" {
		t.Errorf("Expected name 'tmux', got '%s'", spec.Name())
	}

	// Test basic functionality
	width := spec.ColCount('a')
	if width < 0 {
		t.Errorf("ColCount('a') returned negative width: %d", width)
	}
}

func TestJetBrainsSpec(t *testing.T) {
	spec := &JetBrainsSpec{}

	// Test name
	if spec.Name() != "jetbrains" {
		t.Errorf("Expected name 'jetbrains', got '%s'", spec.Name())
	}

	// Test detection with environment variables
	originalEnv := os.Getenv("TERMINAL_EMULATOR")

	// Clean environment
	os.Unsetenv("TERMINAL_EMULATOR")

	// Should not detect without environment variables
	if spec.Detect() {
		t.Error("JetBrainsSpec should not detect without environment variables")
	}

	// Test with TERMINAL_EMULATOR
	os.Setenv("TERMINAL_EMULATOR", "JetBrains-JediTerm")
	if !spec.Detect() {
		t.Error("JetBrainsSpec should detect with TERMINAL_EMULATOR=JetBrains-JediTerm")
	}

	// Test with wrong value
	os.Setenv("TERMINAL_EMULATOR", "SomeOtherTerminal")
	if spec.Detect() {
		t.Error("JetBrainsSpec should not detect with wrong TERMINAL_EMULATOR value")
	}

	// Restore original environment
	if originalEnv != "" {
		os.Setenv("TERMINAL_EMULATOR", originalEnv)
	} else {
		os.Unsetenv("TERMINAL_EMULATOR")
	}

	// Test basic functionality
	width := spec.ColCount('a')
	if width < 0 {
		t.Errorf("ColCount('a') returned negative width: %d", width)
	}
}

func TestGetRegistered(t *testing.T) {
	specs := GetRegistered()
	if len(specs) == 0 {
		t.Error("No terminal specifications registered")
	}

	// Check that all registered specs have names
	names := make(map[string]bool)
	for _, spec := range specs {
		name := spec.Name()
		if name == "" {
			t.Error("Found specification with empty name")
		}
		if names[name] {
			t.Errorf("Duplicate specification name: %s", name)
		}
		names[name] = true
	}

	t.Logf("Registered %d terminal specifications: %v", len(specs), getNames(specs))
}

func getNames(specs []Specification) []string {
	names := make([]string, len(specs))
	for i, spec := range specs {
		names[i] = spec.Name()
	}
	return names
}

func BenchmarkDetectCurrent(b *testing.B) {
	for i := 0; i < b.N; i++ {
		DetectCurrent()
	}
}

func BenchmarkColCount(b *testing.B) {
	spec := DetectCurrent()
	testRunes := []rune{'a', '中', '🚀', '\t'}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, r := range testRunes {
			spec.ColCount(r)
		}
	}
}

// TestUnicodeCharacterWidths tests various Unicode characters and displays
// their visual representation with dots showing the expected column width.
func TestUnicodeCharacterWidths(t *testing.T) {
	spec := DetectCurrent()

	t.Logf("Testing Unicode character widths with %s terminal specification", spec.Name())
	t.Logf("Format: 'character' (U+code) -> width columns [visual representation with dots]")
	t.Logf("========================================================================")

	// Test cases organized by Unicode categories
	testCases := []struct {
		category string
		runes    []rune
	}{
		{
			category: "ASCII Control Characters",
			runes:    []rune{'\t', '\n', '\r', '\b', '\f', '\v', '\a'},
		},
		{
			category: "ASCII Printable Characters",
			runes:    []rune{'A', 'Z', 'a', 'z', '0', '9', ' ', '!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '-', '_', '=', '+'},
		},
		{
			category: "Latin Extended Characters",
			runes:    []rune{'À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë'},
		},
		{
			category: "Greek Characters",
			runes:    []rune{'Α', 'Β', 'Γ', 'Δ', 'Ε', 'Ζ', 'Η', 'Θ', 'α', 'β', 'γ', 'δ', 'ε', 'ζ', 'η', 'θ'},
		},
		{
			category: "Cyrillic Characters",
			runes:    []rune{'А', 'Б', 'В', 'Г', 'Д', 'Е', 'Ё', 'Ж', 'а', 'б', 'в', 'г', 'д', 'е', 'ё', 'ж'},
		},
		{
			category: "CJK (Chinese, Japanese, Korean) Characters",
			runes:    []rune{'中', '文', '日', '本', '한', '국', '語', '言', '字', '漢'},
		},
		{
			category: "Arabic Characters",
			runes:    []rune{'ا', 'ب', 'ت', 'ث', 'ج', 'ح', 'خ', 'د', 'ذ', 'ر'},
		},
		{
			category: "Hebrew Characters",
			runes:    []rune{'א', 'ב', 'ג', 'ד', 'ה', 'ו', 'ז', 'ח', 'ט', 'י'},
		},
		{
			category: "Mathematical Symbols",
			runes:    []rune{'∀', '∂', '∃', '∅', '∇', '∈', '∉', '∋', '∏', '∑', '−', '∓', '∔', '∕', '∖', '∗', '∘', '∙', '√', '∝'},
		},
		{
			category: "Box Drawing Characters",
			runes:    []rune{'─', '━', '│', '┃', '┄', '┅', '┆', '┇', '┈', '┉', '┊', '┋', '┌', '┍', '┎', '┏'},
		},
		{
			category: "Emoji - Faces",
			runes:    []rune{'😀', '😁', '😂', '😃', '😄', '😅', '😆', '😇', '😈', '😉', '😊', '😋'},
		},
		{
			category: "Emoji - Objects",
			runes:    []rune{'🚀', '🚁', '🚂', '🚃', '🚄', '🚅', '🚆', '🚇', '🚈', '🚉', '🚊', '🚋'},
		},
		{
			category: "Emoji - Nature",
			runes:    []rune{'🌟', '🌠', '🌡', '🌢', '🌣', '🌤', '🌥', '🌦', '🌧', '🌨', '🌩', '🌪'},
		},
		{
			category: "Combining Characters",
			runes:    []rune{'\u0300', '\u0301', '\u0302', '\u0303', '\u0304', '\u0305', '\u0306', '\u0307'},
		},
		{
			category: "Zero Width Characters",
			runes:    []rune{'\u200B', '\u200C', '\u200D', '\u2060', '\uFEFF'},
		},
	}

	for _, testCase := range testCases {
		t.Logf("\n--- %s ---", testCase.category)

		for _, r := range testCase.runes {
			width := spec.ColCount(r)
			visual := generateVisualRepresentation(width)

			// Handle special characters for display
			displayChar := string(r)
			switch r {
			case '\t':
				displayChar = "\\t"
			case '\n':
				displayChar = "\\n"
			case '\r':
				displayChar = "\\r"
			case '\b':
				displayChar = "\\b"
			case '\f':
				displayChar = "\\f"
			case '\v':
				displayChar = "\\v"
			case '\a':
				displayChar = "\\a"
			case '\u200B':
				displayChar = "ZWSP" // Zero Width Space
			case '\u200C':
				displayChar = "ZWNJ" // Zero Width Non-Joiner
			case '\u200D':
				displayChar = "ZWJ"  // Zero Width Joiner
			case '\u2060':
				displayChar = "WJ"   // Word Joiner
			case '\uFEFF':
				displayChar = "BOM"  // Byte Order Mark
			}

			// Handle combining characters specially
			if r >= '\u0300' && r <= '\u036F' {
				displayChar = "a" + string(r) // Show combining character with base 'a'
			}

			t.Logf("'%s' (U+%04X) -> %d columns [%s]", displayChar, r, width, visual)
		}
	}

	// Test some edge cases
	t.Logf("\n--- Edge Cases ---")
	edgeCases := []struct {
		desc string
		r    rune
	}{
		{"Null character", '\u0000'},
		{"Delete character", '\u007F'},
		{"Non-breaking space", '\u00A0'},
		{"Soft hyphen", '\u00AD'},
		{"Ideographic space", '\u3000'},
		{"Replacement character", '\uFFFD'},
		{"Private use area", '\uE000'},
		{"Surrogate (invalid)", '\uD800'},
		{"Maximum BMP", '\uFFFF'},
	}

	for _, edge := range edgeCases {
		width := spec.ColCount(edge.r)
		visual := generateVisualRepresentation(width)
		t.Logf("%s '%s' (U+%04X) -> %d columns [%s]", edge.desc, string(edge.r), edge.r, width, visual)
	}
}

// generateVisualRepresentation creates a visual representation using dots
// to show the expected column width of a character.
func generateVisualRepresentation(width int) string {
	if width <= 0 {
		return "(zero width)"
	}
	if width > 10 {
		return "(width > 10)"
	}

	result := ""
	for i := 0; i < width; i++ {
		result += "•"
	}
	return result
}
